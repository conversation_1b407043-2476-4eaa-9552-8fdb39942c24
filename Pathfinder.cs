using Il2Cpp;
using MelonLoader;
using UnityEngine;
using UnityEngine.AI;

namespace TestLE;

public static class Pathfinder
{
    public static NavMeshPath? CalculatePath(NavMeshAgent agent, Transform target)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("Pathfinding failed: Player is null");
            return null;
        }

        if (target == null)
        {
            MelonLogger.Msg("Pathfinding failed: Target is null");
            return null;
        }

        if (agent == null)
        {
            MelonLogger.Msg("Pathfinding failed: Agent is null");
            return null;
        }

        var lineRenderer = new GameObject("Pathfinder Visualizer").AddComponent<LineRenderer>();
        lineRenderer.startColor = Color.green;
        lineRenderer.endColor = Color.green;

        var path = new NavMeshPath();
        if (agent.CalculatePath(target.position, path))
        {
            lineRenderer.positionCount = path.corners.Length;
            lineRenderer.SetPositions(path.corners);
            MelonLogger.Msg($"Path found: Start: {path.corners[0]}, End: {path.corners[^1]}");

            return path;

            // PLAYER.movingState.MoveToPointNoChecks(target.position, false);

            // var closestIndex = GetClosestPathIndex(path.corners, agent.transform.position);
            // MelonLogger.Msg($"Closest index: {closestIndex}, Closest position: {path.corners[closestIndex]}");

            // while (Vector3.Distance(agent.transform.position, target.position) > 1.0f)
            // {
            //     var worldPosition = path.corners[closestIndex]; // The world position
            //     var screenPosition = Camera.main.WorldToScreenPoint(worldPosition); // Convert the world position to screen space
            //     var ray = Camera.main.ScreenPointToRay(screenPosition); // Convert the screen position to a Ray
            //
            //     // Now you can use the ray with the MouseClickMoveCommand method
            //     Player.movingState.MouseClickMoveCommand(ray, false, -1.0f, false, Vector3.zero, true);
            //
            //     while (agent.hasPath)
            //     {
            //         MelonLogger.Msg($"Agent pathing: {agent.pathPending}, {agent.remainingDistance}");
            //         await Task.Delay(1000);
            //     }
            // }
        }

        MelonLogger.Msg("Path not found!");
        return null;
    }

    private static int GetClosestPathIndex(IList<Vector3> path, Vector3 position)
    {
        if (path.Count == 0)
            return -1;

        var closestCorner = 0;
        var minDistance = Vector3.Distance(position, path[0]);

        for (var i = 0; i < path.Count; i++)
        {
            var distance = Vector3.Distance(position, path[i]);
            if (distance < minDistance)
            {
                minDistance = distance;
                closestCorner = i;
            }
        }

        return closestCorner;
    }
}
