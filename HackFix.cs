namespace System.Runtime.CompilerServices
{
    [System.AttributeUsage(
        System.AttributeTargets.Class |
        System.AttributeTargets.Struct |
        System.AttributeTargets.Field |
        System.AttributeTargets.Property |
        System.AttributeTargets.Parameter |
        System.AttributeTargets.ReturnValue,
        AllowMultiple = false)]
    internal sealed class NullableAttribute : Attribute
    {
        public readonly byte[] NullableFlags;
        public NullableAttribute(byte flag) { }
        public NullableAttribute(byte[] flags) { NullableFlags = flags; }
    }
}
