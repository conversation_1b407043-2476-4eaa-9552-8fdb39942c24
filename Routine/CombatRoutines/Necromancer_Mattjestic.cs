using System.Collections;
using Il2Cpp;
using MelonLoader;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routines.CombatRoutines;

public class Necromancer_Mattjestic : CombatRoutine
{
    public override float PotionHealthUse => 0f;
    public override bool PickupPotions => false;
    public override int CombatDistance => 20;
    public override int MovementSkillIndex => 3;

    private const string WRAITHLORD_NAME = "v_SummonedWraithlord(Clone)";
    private const string WRAITH_NAME = "v_SummonedWraith(Clone)";
    private Transform? _wraithlordTransform { get; set; }
    // private DateTime _lastPotionUse { get; set; } = DateTime.MinValue;


    public override IEnumerator OnNewArea()
    {
        yield return RespawnWraithlord();
    }

    public override IEnumerator Run(Enemy enemy, Transform enemyTransform, float distance)
    {
        if (_wraithlordTransform == null)
        {
            yield return RespawnWraithlord();
            yield break;
        }

        MelonLogger.Msg($"Summons: {HasActiveOtherMinions()}");

        var hasWraithlordDreadShade = HasWraithlordDreadShade();
        var hasWraithlordInfernalShade = HasWraithlordInfernalShade();
        var hasBothShades = hasWraithlordDreadShade && hasWraithlordInfernalShade;

        if (!PlayerHelpers.IsAbilityOnCooldown(2) && !hasWraithlordDreadShade && !IsOtherMinionOnTopOfWraithlord())
        {
            PlayerHelpers.UseAbility(2, _wraithlordTransform);
            yield return new WaitForSeconds(0.5f);
        }
        else if (!hasWraithlordInfernalShade && !IsOtherMinionOnTopOfWraithlord())
        {
            PlayerHelpers.UseAbility(1, _wraithlordTransform);
            yield return new WaitForSeconds(0.5f);
        }
        else if (!PlayerHelpers.IsAbilityOnCooldown(3) && hasBothShades && distance <= 20)
        {
            PlayerHelpers.UseMovementAbility(UnityHelpers.RandomPointOnEdgeOfCircleOnNavMesh(enemyTransform.position, 3f, out var movePos) ? movePos : enemyTransform.position);
        }
        else if (!PlayerHelpers.IsAbilityOnCooldown(4) && hasBothShades)
        {
            PlayerHelpers.UseAbility(4, _wraithlordTransform);
        }
        // else if (PLAYER.healthPotion.currentCharges > 0 && DateTime.Now - _lastPotionUse >= TimeSpan.FromSeconds(1))
        // {
    }

    // Parameterless coroutine for modular manager use
    public override IEnumerator Run()
    {
        while (PLAYER != null)
        {
            var (enemy, distance) = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 100);
            if (enemy == null || enemy.Data == null)
            {
                yield return new WaitForSeconds(1f);
                continue;
            }
            yield return Run(enemy, enemy.Data.transform, distance);
        }
    }
        //     PlayerHelpers.UsePotion();
        //     _lastPotionUse = DateTime.Now;
        // }
        else if (HasActiveOtherMinions())
        {
            PlayerHelpers.MoveTo(UnityHelpers.RandomPointOnEdgeOfCircleOnNavMesh(_wraithlordTransform.position, 3f, out var movePos) ? movePos : _wraithlordTransform.position);
            yield return new WaitForSeconds(0.3333f);
            yield break;
        }
        else
        {
            var pos = enemyTransform.position;
            PLAYER.usingAbilityState.HandleMinionCommand(enemy.Data, pos);

            yield return new WaitForSeconds(0.3333f);
            PlayerHelpers.MoveTo(UnityHelpers.RandomPointOnEdgeOfCircleOnNavMesh(pos, 10f, out var movePos) ? movePos : pos);
        }

        yield return new WaitForSeconds(0.1f);
    }

    private IEnumerator RespawnWraithlord()
    {
        PlayerHelpers.UseAbility(0, PLAYER.transform);
        yield return new WaitForSeconds(0.5f);
        _wraithlordTransform = GetWraithlordTransform();
        yield return new WaitForSeconds(0.1f);
    }

    private bool IsOtherMinionOnTopOfWraithlord()
    {
        foreach (var m in PLAYER.clientSummonTracker.clientMinionCreationReferences)
        {
            if (m.name == WRAITHLORD_NAME)
                continue;

            // We don't want to use the buff abilities on "v_SummonedWraith(Clone)" or other minions, only "v_SummonedWraithlord(Clone)"
            if (Vector3.Distance(_wraithlordTransform!.position, m.transform.position) <= 2f)
            {
                return true;
            }
        }

        return false;
    }

    private static Transform? GetWraithlordTransform()
    {
        foreach (var m in PLAYER.clientSummonTracker.clientMinionCreationReferences)
        {
            if (m.name == WRAITHLORD_NAME && Vector3.Distance(PLAYER.transform.position, m.transform.position) <= 10f)
                return m.transform;
        }

        return null;
    }

    private bool HasWraithlordInfernalShade()
    {
        var buffs = _wraithlordTransform!.parent.GetComponentsInChildren<AbilitySync>();
        return buffs.Where(b => b.isActiveAndEnabled).Any(b => b.debugAbilityName == "InfernalShade");

        // var buffs = _wraithlordTransform!.parent.GetComponentsInChildren<AbilityVisuals>();
        // return buffs.Where(b => b.isActiveAndEnabled).Any(b => b.abilitySync.debugAbilityName == "InfernalShade");

        // var abilitySyncObject = _wraithlordTransform!.parent.GetComponent<AbilitySync>();
        // if (abilitySyncObject == null)
        //     return false;
        //
        // return abilitySyncObject.transform.FindChild("v_InfernalShade(Clone)") != null;
    }

    private bool HasWraithlordDreadShade()
    {
        var buffs = _wraithlordTransform!.parent.GetComponentsInChildren<AbilitySync>();
        return buffs.Where(b => b.isActiveAndEnabled).Any(b => b.debugAbilityName == "DreadShade");

        // var buffs = _wraithlordTransform!.parent.GetComponentsInChildren<AbilityVisuals>();
        // return buffs.Where(b => b.isActiveAndEnabled).Any(b => b.abilitySync.debugAbilityName == "DreadShade");

        // var abilitySyncObject = _wraithlordTransform!.parent.GetComponent<AbilitySync>();
        // if (abilitySyncObject == null)
        //     return false;
        //
        // return abilitySyncObject.transform.FindChild("v_DreadShade(Clone)") != null;
    }

    private static bool HasActiveOtherMinions()
    {
        foreach (var m in PLAYER.clientSummonTracker.clientMinionCreationReferences)
            if (m.name != WRAITHLORD_NAME && m.name != WRAITH_NAME && !m.GetComponent<ActorVisuals>().dead)
                return true;

        return false;
    }
}
