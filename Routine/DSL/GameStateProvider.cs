using System.Collections;
using Il2Cpp;
using TestLE.Utilities;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace TestLE.Routine.DSL;

/// <summary>
/// Provides a clean abstraction layer over game state queries for the DSL system.
/// This centralizes all game state access and makes it easier to extend the DSL with new conditions.
/// </summary>
public static class GameStateProvider
{
    #region Player State
    
    public static float PlayerHealth => PLAYER?.healthState?.currentHealth ?? 0f;
    public static float PlayerMaxHealth => PLAYER?.healthState?.maxHealth ?? 1f;
    public static float PlayerHealthPercentage => PlayerMaxHealth > 0 ? PlayerHealth / PlayerMaxHealth : 0f;
    public static Vector3 PlayerPosition => PLAYER?.transform?.position ?? Vector3.zero;
    public static float PlayerVelocity => PLAYER?.movingState?.myAgent?.velocity.magnitude ?? 0f;
    public static bool HasPotions => PLAYER?.healthPotion?.currentCharges > 0;
    public static int PotionCount => PLAYER?.healthPotion?.currentCharges ?? 0;
    
    public static bool IsAbilityOnCooldown(int index)
    {
        return PlayerHelpers.IsAbilityOnCooldown(index);
    }
    
    #endregion
    
    #region Game State
    
    public static bool DeathScreenActive
    {
        get
        {
            var deathScreen = FindHelpers.FindDeathScreen();
            return deathScreen != null && deathScreen.isActiveAndEnabled;
        }
    }
    
    public static bool MonolithCompleteButtonExists => FindHelpers.FindMonolithCompleteButton() != null;
    
    public static string CurrentSceneName => SceneManager.GetActiveScene().name;
    
    public static bool IsInMonolithHub => CurrentSceneName == "M_Rest";
    
    #endregion
    
    #region Enemy State
    
    public static int EnemyCount => ENEMIES?.Count ?? 0;
    
    public static (Enemy? enemy, float distance) GetNearestEnemy(float maxDistance = 100f)
    {
        return FindHelpers.FindNearestEnemy(PlayerPosition, maxDistance);
    }
    
    public static float NearestEnemyDistance
    {
        get
        {
            var (_, distance) = GetNearestEnemy();
            return distance;
        }
    }
    
    public static Enemy? NearestEnemy
    {
        get
        {
            var (enemy, _) = GetNearestEnemy();
            return enemy;
        }
    }
    
    #endregion
    
    #region Loot and Items
    
    public static bool LootItemsNearby => GROUND_ITEMS?.Count > 0;
    public static int LootItemCount => GROUND_ITEMS?.Count ?? 0;
    
    #endregion
    
    #region Interactables
    
    public static bool InteractablesInRange(float range)
    {
        if (INTERACTABLES == null) return false;
        
        foreach (var interactable in INTERACTABLES)
        {
            if (interactable == null || !interactable.isActiveAndEnabled) continue;
            
            var distance = Vector3.Distance(PlayerPosition, interactable.transform.position);
            if (distance <= range) return true;
        }
        
        return false;
    }
    
    public static int InteractableCount => INTERACTABLES?.Count ?? 0;
    
    #endregion
    
    #region Shrines and Objectives
    
    public static bool GoodShrinesAvailable => GOOD_SHRINES?.Count > 0;
    public static int GoodShrineCount => GOOD_SHRINES?.Count ?? 0;
    
    public static bool MonolithObjectivesExist => MONOLITH_OBJECTIVES?.Count > 0;
    public static int MonolithObjectiveCount => MONOLITH_OBJECTIVES?.Count ?? 0;
    
    public static string? ObjectiveType
    {
        get
        {
            var objective = MONOLITH_OBJECTIVES?.FirstOrDefault();
            if (objective == null) return null;
            
            if (objective.GetEnemyObjective() != null) return "enemy";
            if (objective.GetClickObjective() != null) return "click";
            return "unknown";
        }
    }
    
    #endregion
    
    #region Minion State (for Necromancer)
    
    public static bool HasActiveOtherMinions()
    {
        // Implementation from existing Necromancer routine
        var minions = UnityEngine.Object.FindObjectsOfType<ActorVisuals>()
            .Where(x => x.gameObject.name.Contains("Summon") && !x.gameObject.name.Contains("Wraithlord"))
            .ToList();
        
        return minions.Count > 0;
    }
    
    public static bool HasWraithlordDreadShade()
    {
        // Implementation from existing Necromancer routine
        var wraithlord = FindWraithlord();
        if (wraithlord == null) return false;
        
        var shades = UnityEngine.Object.FindObjectsOfType<ActorVisuals>()
            .Where(x => x.gameObject.name.Contains("DreadShade"))
            .ToList();
        
        return shades.Any(shade => Vector3.Distance(wraithlord.position, shade.transform.position) <= 5f);
    }
    
    public static bool HasWraithlordInfernalShade()
    {
        // Implementation from existing Necromancer routine
        var wraithlord = FindWraithlord();
        if (wraithlord == null) return false;
        
        var shades = UnityEngine.Object.FindObjectsOfType<ActorVisuals>()
            .Where(x => x.gameObject.name.Contains("InfernalShade"))
            .ToList();
        
        return shades.Any(shade => Vector3.Distance(wraithlord.position, shade.transform.position) <= 5f);
    }
    
    public static Transform? FindWraithlord()
    {
        const string wraithlordName = "v_SummonedWraithlord(Clone)";
        var wraithlord = UnityEngine.Object.FindObjectsOfType<ActorVisuals>()
            .FirstOrDefault(x => x.gameObject.name == wraithlordName);
        
        return wraithlord?.transform;
    }
    
    public static bool IsOtherMinionOnTopOfWraithlord()
    {
        var wraithlord = FindWraithlord();
        if (wraithlord == null) return false;
        
        var otherMinions = UnityEngine.Object.FindObjectsOfType<ActorVisuals>()
            .Where(x => x.gameObject.name.Contains("Summon") && !x.gameObject.name.Contains("Wraithlord"))
            .ToList();
        
        return otherMinions.Any(minion => Vector3.Distance(wraithlord.position, minion.transform.position) <= 3f);
    }
    
    #endregion
    
    #region Utility Methods
    
    public static float DistanceTo(Vector3 position)
    {
        return Vector3.Distance(PlayerPosition, position);
    }
    
    public static float TimeSince(DateTime time)
    {
        return (float)(DateTime.Now - time).TotalSeconds;
    }
    
    #endregion
}
