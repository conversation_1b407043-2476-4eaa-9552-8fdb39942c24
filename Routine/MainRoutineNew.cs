// using System.Collections;
// using Il2Cpp;
// using <PERSON>onLoader;
// using TestLE.Utilities;
// using UnityEngine;
// using UnityEngine.SceneManagement;
// using Random = UnityEngine.Random;
//
// namespace TestLE.Routines;
//
// public class MainRoutine : IEnumerator
// {
//     public object? Current { get; private set; }
//
//     private DateTime _lastStoreMaterialsTime { get; set; } = DateTime.MinValue;
//     private float _idleTime { get; set; }
//     private Enemy? _enemy { get; set; }
//     private float _enemyDistance { get; set; } = float.MaxValue;
//
//
//     public bool MoveNext()
//     {
//         Current = Main();
//         return true;
//     }
//
//     public void Reset()
//     {
//         Current = null;
//     }
//
//     private IEnumerator Main()
//     {
//         if (PLAYER == null)
//         {
//             MelonLogger.Msg("Player is null!");
//             yield break;
//         }
//
//         // Find nearest enemy
//         SetNearestEnemy();
//
//         // Store materials every 10 seconds
//         if (_lastStoreMaterialsTime == DateTime.MinValue || (DateTime.Now - _lastStoreMaterialsTime).TotalSeconds >= 10)
//         {
//             PlayerHelpers.StoreMaterials();
//             _lastStoreMaterialsTime = DateTime.Now;
//         }
//
//         // Move to random position if idle for 10 seconds
//         if (PLAYER.movingState.myAgent.velocity.magnitude <= 0.1f)
//         {
//             _idleTime += Time.deltaTime;
//             if (_idleTime >= 10f)
//             {
//                 PlayerHelpers.MoveTo(PLAYER.transform.position + new Vector3(Random.Range(-50, 50), 0, Random.Range(-50, 50)));
//                 _idleTime = 0;
//                 yield return new WaitForSeconds(2f);
//             }
//         }
//         else
//         {
//             _idleTime = 0;
//         }
//
//         switch (SceneManager.GetActiveScene().name)
//         {
//             case "EoT":
//             {
//                 yield return HandleScene_EoT();
//                 yield break;
//             }
//             case "MonolithHub":
//             {
//                 yield return HandleScene_MonolithHub();
//                 yield break;
//             }
//             case "M_Rest":
//             {
//                 yield return HandleScene_M_Rest();
//                 yield break;
//             }
//             default:
//             {
//                 yield return HandleScene_Monolith();
//                 yield break;
//             }
//         }
//     }
//
//     private static IEnumerator HandleScene_EoT()
//     {
//         yield break;
//     }
//
//     private IEnumerator HandleScene_MonolithHub()
//     {
//         // Find monolith stone
//         var stone = FindHelpers.FindMonolithStone();
//         if (stone == null)
//         {
//             MelonLogger.Msg("Stone not found!");
//             yield break;
//         }
//
//         // Move to monolith stone
//         yield return MoveToForce(stone.transform.position, stone.interactionRange);
//
//         // Click on monolith stone
//         stone.ObjectClick(PLAYER.gameObject, true);
//         yield return new WaitForSeconds(1f);
//
//         var difficultyButton = FindHelpers.FindMonolithDifficultyButton(FindHelpers.MonolithDifficulty.Empowered);
//         if (difficultyButton != null)
//         {
//             difficultyButton.Press();
//             yield return new WaitForSeconds(1f);
//         }
//         else
//             MelonLogger.Msg("Difficulty button not found, trying normal difficulty");
//
//         // Enter next monolith
//         yield return HandleEnterNextMonolith();
//     }
//
//     private IEnumerator HandleScene_M_Rest()
//     {
//         // Wait for all ground items to be spawned
//         if (LAST_GROUND_ITEM_DROP != DateTime.MinValue && (DateTime.Now - LAST_GROUND_ITEM_DROP).TotalSeconds < 1)
//             yield return new WaitForSeconds(1f);
//
//         // Move and collect XP tomes
//         var tomes = FindHelpers.FindGroundXPTomes();
//         foreach (var t in tomes)
//             yield return MoveToForce(t.transform.position);
//
//         // Click on reward chest
//         var chest = FindHelpers.FindMonolithCompleteRewardChest();
//         if (chest.obj != null && chest.isActive)
//         {
//             yield return MoveToForce(chest.obj.transform.position, chest.obj.interactionRange);
//             chest.obj.ObjectClick(PLAYER.gameObject, true);
//
//             yield break;
//         }
//
//         // Click on reward rock
//         var rock = FindHelpers.FindMonolithCompleteRewardRock();
//         if (rock.obj != null && rock.isActive)
//         {
//             yield return MoveToForce(rock.obj.transform.position, rock.obj.interactionRange);
//             rock.obj.ObjectClick(PLAYER.gameObject, true);
//
//             yield break;
//         }
//
//         // Find monolith stone
//         var stone = FindHelpers.FindMonolithStone();
//         if (stone == null)
//         {
//             MelonLogger.Msg("Stone not found!");
//             yield break;
//         }
//
//         // Move to monolith stone
//         yield return MoveToForce(stone.transform.position, stone.interactionRange);
//
//         // Click on monolith stone
//         stone.ObjectClick(PLAYER.gameObject, true);
//         yield return new WaitForSeconds(1f);
//
//         // Enter next monolith
//         yield return HandleEnterNextMonolith();
//     }
//
//     private IEnumerator HandleScene_Monolith()
//     {
//         // Move to interactable and click it if it is within range
//         for (var i = 0; i < INTERACTABLES.Count; i++)
//         {
//             var interactable = INTERACTABLES[i];
//             if (interactable == null || !interactable.isActiveAndEnabled)
//             {
//                 INTERACTABLES.RemoveAt(i);
//                 i--;
//                 continue;
//             }
//
//             var interactablePos = interactable.transform.position;
//             if (Vector3.Distance(PLAYER.transform.position, interactablePos) > 20f)
//                 continue;
//
//             yield return HandleInteractable(interactable);
//             yield break;
//         }
//
//         // Find monolith completed button
//         var button = FindHelpers.FindMonolithCompleteButton();
//         if (button != null)
//         {
//             yield return HandleMonolithCompleted();
//             yield break;
//         }
//
//         // If no enemy found, move to random position, arenas don't spawn enemies until you move around a bit
//         if (_enemy == null)
//         {
//             MelonLogger.Msg("No mobs found!");
//             PlayerHelpers.MoveTo(PLAYER.transform.position + new Vector3(Random.Range(-5, 5), 0, Random.Range(-5, 5)));
//             yield return new WaitForSeconds(1f);
//             yield break;
//         }
//
//         // // Check if we are in combat range, if so, do combat routine
//         // if (_enemyDistance <= 10)
//         // {
//         //     MelonLogger.Msg("Doing combat routine!");
//         //     yield return HandleCombat();
//         //     yield break;
//         // }
//
//         var objective = MONOLITH_OBJECTIVES.FirstOrDefault();
//         if (objective == null)
//         {
//             MelonLogger.Msg("Objective is null!");
//             if (MONOLITH_OBJECTIVES.Count > 0)
//                 MONOLITH_OBJECTIVES.RemoveAt(0);
//
//             yield return HandleCombat();
//             yield break;
//         }
//
//         var enemyObjective = objective.GetEnemyObjective();
//         if (enemyObjective != null)
//         {
//             MelonLogger.Msg("Objective is enemy!");
//             yield return MoveToForce(enemyObjective.transform.position, combatTriggerDistance: 10f);
//             yield break;
//         }
//
//         var clickObjective = objective.GetClickObjective();
//         if (clickObjective != null)
//         {
//             MelonLogger.Msg("Objective is clickable!");
//             yield return MoveToForce(clickObjective.transform.position, clickObjective.interactionRange, combatTriggerDistance: 10f);
//             clickObjective.ObjectClick(PLAYER.gameObject, true);
//
//             yield break;
//         }
//
//         // If no objective found, default to combat routine
//         yield return HandleCombat();
//     }
//
//     private IEnumerator HandleEnterNextMonolith()
//     {
//         // Find monolith islands
//         var islands = FindHelpers.FindMonolithIslands();
//         if (islands.Count == 0)
//         {
//             MelonLogger.Msg("Islands not found!");
//             yield break;
//         }
//
//         // Click on next island
//         foreach (var (_, ui) in islands)
//         {
//             if (ui.island.completed)
//                 continue;
//
//             if (ui.island.islandType is not EchoWebIsland.IslandType.Normal and not EchoWebIsland.IslandType.Arena and not EchoWebIsland.IslandType.Beacon)
//                 continue;
//
//             var hasConnectionWithComepletedIsland = false;
//             foreach (var c in ui.island.connectedHexes)
//             {
//                 var connectedIsland = islands.GetValueOrDefault(c);
//                 if (connectedIsland == null)
//                     continue;
//
//                 if (!connectedIsland.island.completed)
//                     continue;
//
//                 hasConnectionWithComepletedIsland = true;
//                 break;
//             }
//
//             if (!hasConnectionWithComepletedIsland)
//                 continue;
//
//             MelonLogger.Msg($"Next monolith reward: {(ui.rewards.Count > 0 ? ui.rewards.getAtIndexOrFirst(0).rewardType : "NULL")}");
//             ui.rightClicked();
//             yield return new WaitForSeconds(2f);
//             break;
//         }
//     }
//
//     private IEnumerator HandleMonolithCompleted()
//     {
//         yield return new WaitForSeconds(3f);
//
//         // Wait for all ground items to be spawned
//         if (LAST_GROUND_ITEM_DROP != DateTime.MinValue && (DateTime.Now - LAST_GROUND_ITEM_DROP).TotalSeconds < 1)
//             yield break;
//
//         // Make portal
//         PlayerHelpers.UsePortal();
//         yield return new WaitForSeconds(0.5f);
//
//         // Find portal
//         var portal = FindHelpers.FindMonolithPortal();
//         if (portal == null)
//         {
//             MelonLogger.Msg("Portal not found!");
//             yield break;
//         }
//
//         // // Loot all ground items before moving to portal
//         // while (GROUND_ITEMS.Count > 0)
//         // {
//         //     yield return HandleLoot();
//         //     yield return new WaitForSeconds(0.1f);
//         // }
//
//         // Move to portal
//         yield return MoveToForce(portal.transform.position);
//
//         // Click on portal
//         portal.ObjectClick(PLAYER.gameObject, true);
//         yield return new WaitForSeconds(1f);
//
//         // Clean up
//         ENEMIES.Clear();
//         MONOLITH_OBJECTIVES.Clear();
//         GROUND_ITEMS.Clear();
//     }
//
//     private IEnumerator HandleCombat()
//     {
//         if (_enemy == null)
//         {
//             MelonLogger.Msg("Enemy is null!");
//             yield return new WaitForSeconds(1f);
//             yield break;
//         }
//
//         if (_enemy.Data == null)
//         {
//             MelonLogger.Msg("Enemy data is null!");
//             _enemy.RemoveEnemy();
//             _enemy = null;
//             _enemyDistance = float.MaxValue;
//
//             yield return new WaitForSeconds(1f);
//             yield break;
//         }
//
//         if (CURRENT_ROUTINE == null)
//         {
//             MelonLogger.Msg("Current routine is null!");
//             yield return new WaitForSeconds(1f);
//             yield break;
//         }
//
//         // if (distance <= 1f && !enemy.Data.isActiveAndEnabled)
//         if (_enemyDistance <= 1f && _enemy.Data.Data.actorName != "Exiled Mage" && (!_enemy.Data.actorSync.gameObject.active || !_enemy.Data.isActiveAndEnabled))
//         {
//             MelonLogger.Msg("Enemy is too close and not active!");
//             _enemy.RemoveEnemy();
//             yield break;
//         }
//
//         var enemyTransform = _enemy.Data.transform;
//         if (_enemy.Data.gameObject.active)
//         {
//             yield return CURRENT_ROUTINE.Run(_enemy, enemyTransform, _enemyDistance);
//         }
//         else
//         {
//             PlayerHelpers.MoveTo(enemyTransform.position);
//
//             if (_enemy.Data.Data.actorName != "Exiled Mage")
//             {
//                 var mageInteract = INTERACTABLES.GetClosestToPlayer();
//                 if (mageInteract != null)
//                 {
//                     yield return HandleInteractable(mageInteract);
//                     yield return new WaitForSeconds(1f);
//                 }
//             }
//             else
//             {
//                 yield return new WaitForSeconds(0.3333f);
//             }
//         }
//     }
//
//     private IEnumerator HandleLoot()
//     {
//         MelonLogger.Msg("Handling loot!");
//         var groundItem = GROUND_ITEMS.FirstOrDefault();
//         if (groundItem == null)
//         {
//             MelonLogger.Msg("Ground item is null!");
//             if (GROUND_ITEMS.Count > 0)
//                 GROUND_ITEMS.RemoveAt(0);
//
//             yield break;
//         }
//
//         var pos = groundItem.GetPos();
//         if (pos == null)
//         {
//             MelonLogger.Msg("Position is null!");
//             if (GROUND_ITEMS.Count > 0)
//                 GROUND_ITEMS.RemoveAt(0);
//
//             yield break;
//         }
//
//         MelonLogger.Msg("Moving to ground item!");
//         yield return MoveToForce(groundItem.GetPos()!.Value, canTriggerLooter: false);
//         groundItem.Pickup();
//     }
//
//     private IEnumerator HandleInteractable(WorldObjectClickListener interactable)
//     {
//         yield return MoveToForce(interactable.transform.position, interactable.interactionRange, canTriggerLooter: false);
//         INTERACTABLES.Remove(interactable);
//         interactable.ObjectClick(PLAYER.gameObject, true);
//     }
//
//     /// <summary>
//     /// Will force the player to move to the position, looping until the player is within the stoppingDistance or maxTries is reached.
//     /// </summary>
//     /// <param name="position">Position to move to</param>
//     /// <param name="stoppingDistance">Distance to stop moving</param>
//     /// <param name="maxTries">Max number of tries</param>
//     /// <param name="delayBetweenTries">Delay between tries</param>
//     /// <param name="combatTriggerDistance">Distance to trigger combat</param>
//     /// <param name="canTriggerLooter">Whether it can trigger looter</param>
//     /// <returns>IEnumerator for coroutine</returns>
//     public IEnumerator MoveToForce(Vector3 position, float stoppingDistance = 1f, int maxTries = 30, float delayBetweenTries = 0.3333f, float combatTriggerDistance = 3f, bool canTriggerLooter = true)
//     {
//         if (PLAYER == null)
//         {
//             MelonLogger.Msg("PLAYER is null");
//             yield break;
//         }
//
//         for (var i = 0; i < maxTries; i++)
//         {
//             SetNearestEnemy();
//
//             // Check if we are in danger, if so, do combat routine
//             if (combatTriggerDistance >= 0f && _enemyDistance <= combatTriggerDistance)
//             {
//                 yield return HandleCombat();
//                 i--;
//                 continue;
//             }
//
//             // Loot ground items
//             if (GROUND_ITEMS.Count > 0 && canTriggerLooter)
//             {
//                 yield return HandleLoot();
//                 i--;
//                 continue;
//             }
//
//             if (Vector3.Distance(PLAYER.transform.position, position) <= stoppingDistance)
//                 break;
//
//             PlayerHelpers.MoveTo(position);
//             yield return new WaitForSeconds(delayBetweenTries);
//         }
//     }
//
//     private void SetNearestEnemy()
//     {
//         (_enemy, _enemyDistance) = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 100);
//         // _enemyDistance = _enemy != null ? Vector3.Distance(PLAYER.transform.position, _enemy.Data.transform.position) : float.MaxValue;
//     }
// }


