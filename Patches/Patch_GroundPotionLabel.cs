using Il2Cpp;
using <PERSON><PERSON><PERSON>oa<PERSON>;

namespace TestLE.Patches;

// ReSharper disable once UnusedType.Global
public sealed class Patch_GroundPotionLabel : Patch
{
    public override void Setup()
    {
        Patches_GroundPotionLabel.OnSetGroundTooltipTextPostfix_ovl1 += OnSetGroundTooltipTextPostfix;
    }

    private static void OnSetGroundTooltipTextPostfix(GroundPotionLabel __instance)
    {
        MelonLogger.Msg("OnSetGroundTooltipTextPostfix: Potion found!");
        
        WaitForSeconds(1f, () =>
        {
            if (!__instance.gameObject.active || CURRENT_ROUTINE is not { PickupPotions: true } || PLAYER.healthPotion.currentCharges >= PLAYER.healthPotion.maxCharges)
                return;
            
            _ = new GroundItem(__instance);
            MelonLogger.Msg("OnSetGroundTooltipTextPostfix: Potion added!");
        });
    }
}


// using System.Collections;
// using HarmonyLib;
// using Il2Cpp;
// using MelonLoader;
// using UnityEngine;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(GroundPotionLabel))]
// public class Patch_GroundPotionLabel
// {
//     [HarmonyPostfix]
//     [HarmonyPatch("SetGroundTooltipText")]
//     private static void SetGroundTooltipText_Postfix(GroundPotionLabel __instance)
//     {
//         MelonCoroutines.Start(Wait(__instance));
//     }
//
//     private static IEnumerator Wait(GroundPotionLabel __instance)
//     {
//         yield return new WaitForSeconds(1f);
//
//         if (!__instance.gameObject.active || CURRENT_ROUTINE == null || !CURRENT_ROUTINE.PickupPotions || PLAYER.healthPotion.currentCharges >= PLAYER.healthPotion.maxCharges)
//             yield break;
//
//         _ = new GroundItem(__instance);
//     }
// }
