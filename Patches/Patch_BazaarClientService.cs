using Il2CppInterop.Runtime.InteropTypes.Arrays;
using Il2CppLE.Services.Bazaar;

namespace TestLE.Patches;

// ReSharper disable once UnusedType.Global
public sealed class Patch_BazaarClientService : Patch
{
    public override void Setup()
    {
        Patches_BazaarClientService.OnHandleBazaarResponsePostfix_ovl1 += OnHandleBazaarResponsePostfix;
    }

    private static void OnHandleBazaarResponsePostfix(BazaarClientService __instance, ushort requestId, BazaarResponseType responseType, Il2CppStructArray<byte> data)
    {
        AUCTION_HOUSE = new AuctionHouse();
    }
}

// using HarmonyLib;
// using Il2CppInterop.Runtime.InteropTypes.Arrays;
// using Il2CppLE.Services.Bazaar;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(BazaarClientService))]
// public class Patch_BazaarClientService
// {
//     [HarmonyPostfix]
//     [HarmonyPatch("HandleBazaarResponse", typeof(ushort), typeof(BazaarResponseType), typeof(Il2CppStructArray<byte>))]
//     private static void HandleBazaarResponse_Postfix(BazaarClientService __instance, ushort requestId, BazaarResponseType responseType, Il2CppStructArray<byte> data)
//     {
//         AUCTION_HOUSE = new AuctionHouse();
//     }
// }
