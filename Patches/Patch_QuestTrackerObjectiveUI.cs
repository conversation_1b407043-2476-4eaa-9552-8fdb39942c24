// using Il2Cpp;
// using HarmonyLib;
// using Il2CppTMPro;
// using <PERSON>onLoader;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(QuestTrackerObjectiveUI), "SetObjective", typeof(StatefulObjective), typeof(bool))]
// public class Patch_QuestTrackerObjectiveUI_SetObjective
// {
//     public static void Postfix(QuestTrackerObjectiveUI __instance)
//     {
//         MelonLogger.Msg("SetObjective called!");
//         if (__instance.transform.parent.GetComponent<TextMeshProUGUI>().text != "Monolith of Fate")
//             return;
//
//         MelonLogger.Msg("Monolith of Fate found!");
//     }
// }
//
// [HarmonyPatch(typeof(QuestTrackerObjectiveUI), "setText", typeof(string))]
// public class Patch_QuestTrackerObjectiveUI_setText
// {
//     public static void Postfix(QuestTrackerObjectiveUI __instance)
//     {
//         MelonLogger.Msg("setText called!");
//         if (__instance.transform.parent.GetComponent<TextMeshProUGUI>().text != "Monolith of Fate")
//             return;
//
//         MelonLogger.Msg("Monolith of Fate found!");
//         OBJECTIVE
//     }
// }
//
// [HarmonyPatch(typeof(QuestTrackerObjectiveUI), "UpdateObjective")]
// public class Patch_QuestTrackerObjectiveUI_UpdateObjective
// {
//     public static void Postfix(QuestTrackerObjectiveUI __instance)
//     {
//         MelonLogger.Msg("UpdateObjective called!");
//         if (__instance.transform.parent.GetComponent<TextMeshProUGUI>().text != "Monolith of Fate")
//             return;
//
//         MelonLogger.Msg("Monolith of Fate found!");
//     }
// }


