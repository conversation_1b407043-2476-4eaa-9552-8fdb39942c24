using Il2CppLE.Interactions.KeyProviders;
using MelonLoader;
using TestLE.Utilities;

namespace TestLE.Patches;

// ReSharper disable once UnusedType.Global
public sealed class Patch_DungeonExitKeyProvider : Patch
{
    public override void Setup()
    {
        Patches_DungeonExitKeyProvider.OnGetMessageKeyPostfix_ovl1 += OnGetMessageKeyPostfix;
    }

    private static void OnGetMessageKeyPostfix(DungeonExitKeyProvider __instance, ref string __result)
    {
        WaitForSeconds(3f, () =>
        {
            if (!__instance.isActiveAndEnabled)
                return;
            
            MelonLogger.Msg("Dungeon Exit");
            MinimapHelpers.CreateIconForDungeonExit(__instance);
        });
    }
}

// using System.Collections;
// using HarmonyLib;
// using Il2Cpp;
// using Il2CppLE.Interactions.KeyProviders;
// using MelonLoader;
// using TestLE.Utilities;
// using UnityEngine;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(DungeonExitKeyProvider))]
// public class Patch_DungeonExitKeyProvider
// {
//     [HarmonyPostfix]
//     [HarmonyPatch("GetMessageKey")]
//     private static void GetMessageKey_Postfix(DungeonExitKeyProvider __instance)
//     {
//         MelonCoroutines.Start(Wait(__instance));
//     }
//
//     private static IEnumerator Wait(DungeonExitKeyProvider __instance)
//     {
//         yield return new WaitForSeconds(3f);
//         if (__instance.isActiveAndEnabled)
//         {
//             MelonLogger.Msg("Dungeon Exit");
//             MinimapHelpers.CreateIconForDungeonExit(__instance);
//         }
//     }
// }
