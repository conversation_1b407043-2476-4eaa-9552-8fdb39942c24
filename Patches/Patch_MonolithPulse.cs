using Il2Cpp;
using Il2CppCysharp.Threading.Tasks;
using Il2CppLE.Gameplay.Monolith.Frontend;
using MelonLoader;
using UnityEngine;

namespace TestLE.Patches;

// ReSharper disable once UnusedType.Global
public sealed class Patch_MonolithPulse : Patch
{
    public override void Setup()
    {
        Patches_MonolithPulse.OnReceiveShowPostfix_ovl1 += OnReceiveShowPostfix;
        Patches_MonolithPulse.OnReceiveHidePostfix_ovl1 += OnReceiveHidePostfix;
        Patches_MonolithPulse.OnOnDestroyInternalPostfix_ovl1 += OnOnDestroyInternalPostfix;
    }

    private static void OnReceiveShowPostfix(MonolithPulse __instance, Vector3 localPosition, Il2CppSystem.Nullable<DMMapWorldIcon.iconType> icon, ref UniTask __result)
    {
        MelonLogger.Msg("OnReceiveShowPostfix: MonolithPulse ReceiveShow");

        if (__instance.completed)
        {
            MelonLogger.Msg("OnReceiveShowPostfix: MonolithPulse already completed");
            return;
        }

        MelonLogger.Msg("OnReceiveShowPostfix: MonolithPulse initialized");
        _ = new MonolithObjective(__instance);
    }
    
    private static void OnReceiveHidePostfix(MonolithPulse __instance)
    {
        MelonLogger.Msg("OnReceiveHidePostfix: MonolithPulse ReceiveHide");

        if (__instance.completed)
        {
            MelonLogger.Msg("OnReceiveHidePostfix: MonolithPulse already completed");
            return;
        }

        MelonLogger.Msg("OnReceiveHidePostfix: MonolithPulse initialized");
        _ = new MonolithObjective(__instance);
    }
    
    private static void OnOnDestroyInternalPostfix(MonolithPulse __instance)
    {
        MelonLogger.Msg("OnOnDestroyInternalPostfix: MonolithPulse initialized!");
        MONOLITH_OBJECTIVES.RemoveAll(x => x == null || x.MonolithPulse == __instance);
        
    }
}


// using HarmonyLib;
// using Il2CppLE.Gameplay.Monolith.Frontend;
// using MelonLoader;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(MonolithPulse), "ReceiveShow")]
// public class Patch_MonolithPulse_ReceiveShow
// {
//     public static void Postfix(MonolithPulse __instance)
//     {
//         MelonLogger.Msg("MonolithPulse ReceiveShow");
//
//         if (__instance.completed)
//         {
//             MelonLogger.Msg("MonolithPulse already completed");
//             return;
//         }
//
//         MelonLogger.Msg("MonolithPulse initialized");
//         _ = new MonolithObjective(__instance);
//     }
// }
//
// [HarmonyPatch(typeof(MonolithPulse), "ReceiveHide")]
// public class Patch_MonolithPulse_ReceiveHide
// {
//     public static void Postfix(MonolithPulse __instance)
//     {
//         MelonLogger.Msg("MonolithPulse ReceiveHide");
//
//         if (__instance.completed)
//         {
//             MelonLogger.Msg("MonolithPulse already completed");
//             return;
//         }
//
//         MelonLogger.Msg("MonolithPulse initialized");
//         _ = new MonolithObjective(__instance);
//     }
// }
//
// [HarmonyPatch(typeof(MonolithPulse), "OnDestroy")]
// public class Patch_MonolithPulse_OnDestroy
// {
//     public static void Postfix(MonolithPulse __instance)
//     {
//         MONOLITH_OBJECTIVES.RemoveAll(x => x == null || x.MonolithPulse == __instance);
//     }
// }
