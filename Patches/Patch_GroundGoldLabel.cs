using Il2Cpp;
using <PERSON>on<PERSON>oa<PERSON>;

namespace TestLE.Patches;

// ReSharper disable once UnusedType.Global
public sealed class Patch_GroundGoldLabel : Patch
{
    public override void Setup()
    {
        Patches_GroundGoldLabel.OnSetGroundTooltipTextPostfix_ovl1 += OnSetGroundTooltipTextPostfix;
    }

    private static void OnSetGroundTooltipTextPostfix(GroundGoldLabel __instance)
    {
        MelonLogger.Msg("OnSetGroundTooltipTextPostfix: Gold found!");
        
        WaitForSeconds(1f, () =>
        {
            if (!__instance.gameObject.active || __instance.visuals.goldValue < 500)
                return;
            
            _ = new GroundItem(__instance);
            MelonLogger.Msg("OnSetGroundTooltipTextPostfix: Gold added!");
        });
    }
}


// using System.Collections;
// using System.Text.RegularExpressions;
// using HarmonyLib;
// using Il2Cpp;
// using Il2CppItemFiltering;
// using MelonLoader;
// using UnityEngine;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(GroundGoldLabel))]
// public class Patch_GroundGoldLabel
// {
//     [HarmonyPostfix]
//     [HarmonyPatch("SetGroundTooltipText")]
//     private static void SetGroundTooltipText_Postfix(GroundGoldLabel __instance)
//     {
//         MelonCoroutines.Start(Wait(__instance));
//     }
//
//     private static IEnumerator Wait(GroundGoldLabel __instance)
//     {
//         yield return new WaitForSeconds(1f);
//
//         if (!__instance.gameObject.active || __instance.visuals.goldValue < 500)
//             yield break;
//
//         // var match = Regex.Match(__instance.itemText.text, @"\d+");
//         // if (!match.Success)
//         //     yield break;
//         //
//         // var number = int.Parse(match.Value);
//         // if (number <= 500)
//         //     yield break;
//
//         _ = new GroundItem(__instance);
//     }
// }
