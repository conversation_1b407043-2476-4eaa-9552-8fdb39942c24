using System.Collections;
using System.Globalization;
using System.Text.RegularExpressions;
using Il2Cpp;
using Il2CppCysharp.Threading.Tasks;
using Il2CppInterop.Runtime;
using Il2CppLE.Services.Bazaar;
using Il2CppLE.UI.Bazaar;
using MelonLoader;
using TestLE.Utilities;
using UnityEngine;
using CancellationToken = Il2CppSystem.Threading.CancellationToken;

namespace TestLE;

public class AuctionHouse
{
    public ListingsBlockView? ListingParent { get; }
    public QueryItemsResponse? CurrentQueryItemsResponse { get; }
    public List<ItemAH> Items { get; } = new();

    private bool _canSearch { get; }


    public AuctionHouse()
    {
        var listings = FindHelpers.FindBazaarListings();
        if (listings.Count == 0)
        {
            MelonLogger.Msg("No listings found.");
            return;
        }

        ListingParent = listings[0].transform.parent.parent.parent.parent.GetComponent<ListingsBlockView>();
        if (ListingParent == null)
        {
            MelonLogger.Msg("Failed to get listing parent.");
            return;
        }

        CurrentQueryItemsResponse = ListingParent.CurrentQueryItemsResponseDrawn;
        if (CurrentQueryItemsResponse == null)
        {
            MelonLogger.Msg("Failed to get current query items response.");
            return;
        }

        MelonLogger.Msg("Found listings:");
        foreach (var l in listings)
        {
            var itemData = l.itemData;
            Items.Add(new ItemAH(l, itemData));
            MelonLogger.Msg($"Listing: {itemData.FullName}");
        }

        if (Items.Count == 0)
        {
            MelonLogger.Msg("No items found.");
            return;
        }

        _canSearch = true;
    }

    public bool TrySearch(SearchAH search)
    {
        if (!_canSearch)
        {
            MelonLogger.Msg("Cannot search.");
            return true;
        }

        return Search(search);
    }

    private bool Search(SearchAH search)
    {
        var itemsToShow = new Il2CppSystem.Collections.Generic.List<ItemListingData>();
        foreach (var item in Items)
        {
            var foundImplicits = 0;
            var foundAffixes = 0;

            foreach (var a in item.Implicits)
            {
                foreach (var sa in search.Implicits)
                {
                    var isTextMatch = sa.ExactText ? string.Equals(a.text, sa.Text, StringComparison.InvariantCultureIgnoreCase) : a.text.Contains(sa.Text, StringComparison.InvariantCultureIgnoreCase);
                    var isValueInRange = a.value >= sa.ValueMin && (!sa.SearchInRange || a.value <= sa.ValueMax);

                    if (isTextMatch && isValueInRange)
                        foundImplicits++;
                }
            }

            foreach (var a in item.Affixes)
            {
                foreach (var sa in search.Affixes)
                {
                    var isTextMatch = sa.ExactText ? string.Equals(a.text, sa.Text, StringComparison.InvariantCultureIgnoreCase) : a.text.Contains(sa.Text, StringComparison.InvariantCultureIgnoreCase);
                    var isValueInRange = a.value >= sa.ValueMin && (!sa.SearchInRange || a.value <= sa.ValueMax);

                    if (isTextMatch && isValueInRange)
                    {
                        MelonLogger.Msg($"Affix: {a.text}\nValue: {a.value}\n");
                        foundAffixes++;
                    }
                }
            }

            if (foundAffixes == search.Affixes.Count && foundImplicits == search.Implicits.Count)
            {
                itemsToShow.Add(item.Listing.listingData);
            }
        }

        // Sort items
        var compGoldLowFirst = new Comparison<ItemListingData>((item1, item2) => item1.GoldValue.CompareTo(item2.GoldValue));
        itemsToShow.Sort(DelegateSupport.ConvertDelegate<Il2CppSystem.Comparison<ItemListingData>>(compGoldLowFirst));
        // var sortType = ListingParent!.parentUI.filterUI.sortingSelection.SelectedSorting;
        // switch (sortType)
        // {
        //     case SortingSelection.GOLD_LOW_FIRST:
        //     {
        //         var compGoldLowFirst = new Comparison<ItemListingData>((item1, item2) => item1.GoldValue.CompareTo(item2.GoldValue));
        //         itemsToShow.Sort(DelegateSupport.ConvertDelegate<Il2CppSystem.Comparison<ItemListingData>>(compGoldLowFirst));
        //         break;
        //     }
        //     case SortingSelection.GOLD_HIGH_FIRST:
        //     {
        //         var compGoldHighFirst = new Comparison<ItemListingData>((item1, item2) => item2.GoldValue.CompareTo(item1.GoldValue));
        //         itemsToShow.Sort(DelegateSupport.ConvertDelegate<Il2CppSystem.Comparison<ItemListingData>>(compGoldHighFirst));
        //         break;
        //     }
        //     case SortingSelection.NEWEST_FIRST:
        //     {
        //         var compNewestFirst = new Comparison<ItemListingData>((item1, item2) => item2.listedAtUnixSecond.CompareTo(item1.listedAtUnixSecond));
        //         itemsToShow.Sort(DelegateSupport.ConvertDelegate<Il2CppSystem.Comparison<ItemListingData>>(compNewestFirst));
        //         break;
        //     }
        // }

        // Find page buttons to show after reordering list
        var childrenToShow = new List<GameObject>();
        if (itemsToShow.Count == 0)
        {
            var pagesParent = ListingParent!.paginationControls;
            if (pagesParent == null)
            {
                MelonLogger.Msg("Failed to get pages parent.");
                return true;
            }

            for (var i = 0; i < pagesParent.transform.childCount; i++)
            {
                var child = pagesParent.transform.GetChild(i);
                if (child == null)
                {
                    MelonLogger.Msg("Failed to get child.");
                    continue;
                }

                if (child.gameObject.active)
                {
                    childrenToShow.Add(child.gameObject);
                    MelonLogger.Msg($"Found page button: {child.gameObject.name}");
                }
            }
        }

        // Reorder list
        var q = new QueryItemsResponse(itemsToShow, CurrentQueryItemsResponse!.currentPage, CurrentQueryItemsResponse.totalListings, CurrentQueryItemsResponse.totalPages);
        q.PackData();
        ListingParent!.DrawItemList(q, true);

        // Show page buttons
        MelonCoroutines.Start(ShowPageButtons(childrenToShow));

        return itemsToShow.Count > 0;
    }

    private static IEnumerator ShowPageButtons(List<GameObject> childrenToShow)
    {
        yield return new WaitForSeconds(0.5f);

        foreach (var c in childrenToShow)
        {
            c.SetActive(true);
            MelonLogger.Msg($"Showing page button: {c.name}");
        }
    }


    public class ItemAH
    {
        public ListingUI Listing { get; set; }
        public ItemDataUnpacked ItemData { get; set; }

        public List<(string text, float value)> Implicits { get; } = new();
        public List<(string text, float value)> Affixes { get; } = new();


        public ItemAH(ListingUI listing, ItemDataUnpacked itemData)
        {
            Listing = listing;
            ItemData = itemData;

            foreach (var i in listing.implicitText)
            {
                if (!i.isActiveAndEnabled)
                    continue;

                Implicits.Add(ScrapeImplicitOrAffix(i.text));
            }

            foreach (var a in listing.affixesText)
            {
                if (!a.isActiveAndEnabled)
                    continue;

                Affixes.Add(ScrapeImplicitOrAffix(a.text));
            }

            // PrintAffixes();
        }

        private static (string text, float value) ScrapeImplicitOrAffix(string affixText)
        {
            const string regex = @"[\d,]+";

            affixText = Regex.Replace(affixText, "<.*?>", "");

            var cleanText = Regex.Replace(affixText, regex, "").Replace("%", "").Replace("+", "").Replace("-", "").Replace("  ", " ").Trim();
            var match = Regex.Matches(affixText, regex).FirstOrDefault();

            return (cleanText, match != null && float.TryParse(match.Value.Replace(",", "."), out var number) ? number : 0f);
        }

        // private void PrintAffixes()
        // {
        //     foreach (var (display, value) in Affixes)
        //     {
        //         MelonLogger.Msg($"\nAffix: {display}\nValue: {value}\n");
        //     }
        // }
    }

    public record SearchAH(List<AffixFilterAH> Implicits, List<AffixFilterAH> Affixes);

    public record AffixFilterAH(string Text, bool ExactText, float ValueMin, bool SearchInRange, float ValueMax);
}

public class AuctionHouseUI
{
    private List<AuctionHouse.AffixFilterAH> _implicits { get; set; } = new();
    private List<AuctionHouse.AffixFilterAH> _affixes { get; set; } = new();
    private Vector2 _affixesScrollPosition { get; set; } = Vector2.zero;
    private bool _continueTillFound { get; set; } = true;
    private bool _isSearching { get; set; }
    private bool _wantsToCancelSearch { get; set; }
    private int _startPage { get; set; } = 1;
    private int _pagesToSkip { get; set; } = 1;

    private int _desiredPrice { get; set; }
    private bool _allowZeroPrice { get; set; }
    private bool _useItemBase { get; set; }
    private bool _isListingInventory { get; set; }


    public void DrawUI()
    {
        HandleAffixes(true);
        HandleAffixes(false);
        HandleSearching();

        GUILayout.BeginHorizontal();

        GUILayout.Label("Hover Listing Price: ", GUILayout.Width(120), GUILayout.Height(20));
        var valueStr = GUILayout.TextField(_desiredPrice.ToString(CultureInfo.InvariantCulture), GUILayout.Width(100), GUILayout.Height(20));
        if (int.TryParse(valueStr, out var value))
            _desiredPrice = value;

        GUILayout.Label("Allow Zero Price: ", GUILayout.Width(110), GUILayout.Height(20));
        _allowZeroPrice = GUILayout.Toggle(_allowZeroPrice, "", GUILayout.Width(20), GUILayout.Height(20));

        GUILayout.Label("Use Item Base: ", GUILayout.Width(110), GUILayout.Height(20));
        _useItemBase = GUILayout.Toggle(_useItemBase, "", GUILayout.Width(20), GUILayout.Height(20));

        if (GUILayout.Button("List Inventory", GUILayout.Width(100), GUILayout.Height(20)) && !_isListingInventory)
            MelonCoroutines.Start(StartListingInventory());

        GUILayout.EndHorizontal();
    }

    public IEnumerator SearchHoveredItem()
    {
        var inventoryUI = FindHelpers.FindInventoryUI();
        if (inventoryUI == null)
        {
            MelonLogger.Msg("Failed to get inventory UI.");
            yield break;
        }

        if (!inventoryUI.isHovered)
        {
            MelonLogger.Msg("No item hovered.");
            yield break;
        }

        var bazaarUI = FindHelpers.FindBazaarUI();
        if (bazaarUI == null)
        {
            MelonLogger.Msg("Failed to get bazaar UI.");
            yield break;
        }

        var filterUI = bazaarUI.filterUI;
        if (filterUI == null)
        {
            MelonLogger.Msg("Failed to get filter UI.");
            yield break;
        }

        var hoveredItem = inventoryUI.GetUiAtPosition(inventoryUI.lastInventoryPos);
        if (hoveredItem == null || hoveredItem.EntryRef == null || hoveredItem.EntryRef.data == null)
        {
            MelonLogger.Msg("Invalid hovered item.");
            yield break;
        }

        var itemData = hoveredItem.EntryRef.data;
        if (itemData.isUnique())
        {
            MelonLogger.Msg("Unique item.");

            // bazaarUI.filterUI.ActivateAdvancedPanel(true); //TODO: FIX
            yield return new WaitForSeconds(0.2f);

            filterUI.uniquesPicker.SelectedUniques = new[] { (int)itemData.uniqueID };

            if (itemData.legendaryPotential > 0)
                filterUI.legendaryPotentialRange.min.SetText(itemData.legendaryPotential.ToString());

            if (itemData.weaversWill > 0)
                filterUI.weaverWillRange.min.SetText(itemData.weaversWill.ToString());
        }
        else if (itemData.isExaltedItem())
        {
            MelonLogger.Msg("Exalted item.");

            var hasTier7 = false;
            var affixIds = new List<int>();
            foreach (var a in itemData.affixes)
            {
                if (a.affixTier >= 5)
                {
                    affixIds.Add(a.affixId);

                    if (a.affixTier == 6)
                        hasTier7 = true;
                }
            }

            // bazaarUI.filterUI.ActivateAdvancedPanel(true); //TODO: FIX
            yield return new WaitForSeconds(0.2f);

            // filterUI.affixesPicker.SelectedAffixes = affixIds.ToArray(); //TODO: FIX
            filterUI.tierDropdown.dropdown.value = hasTier7 ? 6 : 5;

            // if (_useItemBase)
            //     filterUI.subtypePicker.SelectedSubtypes = new[] { (int)itemData.subType }; //TODO: FIX
        }
        else
        {
            MelonLogger.Msg("Other item.");

            var affixIds = new List<int>();
            foreach (var a in itemData.affixes)
                affixIds.Add(a.affixId);

            // filterUI.affixesPicker.SelectedAffixes = affixIds.ToArray(); //TODO: FIX

            // if (_useItemBase)
            //     filterUI.subtypePicker.SelectedSubtypes = new[] { (int)itemData.subType }; //TODO: FIX
        }

        yield return new WaitForSeconds(0.1f);

        bazaarUI.SearchPress();
        while (bazaarUI.filterUI.sortingSelection.dropdown.value != (int)SortingSelection.GOLD_LOW_FIRST)
        {
            bazaarUI.filterUI.sortingSelection.dropdown.value = (int)SortingSelection.GOLD_LOW_FIRST;
            yield return new WaitForSeconds(0.1f);
        }
    }

    public IEnumerator ListHoveredItem()
    {
        var inventoryUI = FindHelpers.FindInventoryUI();
        if (inventoryUI == null)
        {
            MelonLogger.Msg("Failed to get inventory UI.");
            yield break;
        }

        if (!inventoryUI.isHovered)
        {
            MelonLogger.Msg("No item hovered.");
            yield break;
        }

        // var affixesPicker = FindHelpers.FindBazaarAffixesPicker();
        // if (affixesPicker == null)
        // {
        //     MelonLogger.Msg("Failed to get affixes picker.");
        //     yield break;
        // }

        var bazaarUI = FindHelpers.FindBazaarUI();
        if (bazaarUI == null)
        {
            MelonLogger.Msg("Failed to get bazaar UI.");
            yield break;
        }

        if (_desiredPrice == 0 && !_allowZeroPrice)
        {
            MelonLogger.Msg("Price is zero and zero price is not allowed.");
            yield break;
        }

        inventoryUI.TryQuickMove(inventoryUI.lastInventoryPos);
        yield return new WaitForSeconds(0.1f);

        // bazaarUI.listItemUI.desiredPrice = _desiredPrice; //TODO: FIX
        bazaarUI.listItemUI.ListItem(_desiredPrice, new CancellationToken());
    }

    public IEnumerator StartListingInventory()
    {
        var inventoryUI = FindHelpers.FindInventoryUI();
        if (inventoryUI == null)
        {
            MelonLogger.Msg("Failed to get inventory UI.");
            yield break;
        }

        var bazaarUI = FindHelpers.FindBazaarUI();
        if (bazaarUI == null)
        {
            MelonLogger.Msg("Failed to get bazaar UI.");
            yield break;
        }

        if (_desiredPrice == 0 && !_allowZeroPrice)
        {
            MelonLogger.Msg("Price is zero and zero price is not allowed.");
            yield break;
        }

        _isListingInventory = true;

        var items = new List<ItemContainerEntry>();
        foreach (var i in inventoryUI.container.GetContent())
            items.Add(i);

        MelonLogger.Msg("Listing inventory...");
        while (items.Count > 0)
        {
            var item = items[0];
            inventoryUI.TryQuickMove(item.Position);
            yield return new WaitForSeconds(0.1f);

            // bazaarUI.listItemUI.desiredPrice = _desiredPrice; //TODO: FIX

            var status = bazaarUI.listItemUI.ListItem(_desiredPrice, new CancellationToken());
            while (status.Status == UniTaskStatus.Pending)
                yield return new WaitForSeconds(0.1f);

            items.RemoveAt(0);
            yield return new WaitForSeconds(0.1f);
        }

        _isListingInventory = false;
    }

    private void HandleSearching()
    {
        GUILayout.BeginHorizontal();

        if (AUCTION_HOUSE != null)
        {
            if (_isSearching)
            {
                if (GUILayout.Button("Cancel Search", GUILayout.Width(100), GUILayout.Height(20)))
                {
                    _wantsToCancelSearch = true;
                }
            }
            else
            {
                if (GUILayout.Button("Search", GUILayout.Width(100), GUILayout.Height(20)))
                {
                    _wantsToCancelSearch = false;
                    MelonCoroutines.Start(StartSeach());
                }
            }
        }

        GUILayout.Label("Continue Till Found: ", GUILayout.Width(120), GUILayout.Height(20));
        _continueTillFound = GUILayout.Toggle(_continueTillFound, "", GUILayout.Width(20), GUILayout.Height(20));

        GUILayout.Label("Start Page: ", GUILayout.Width(100), GUILayout.Height(20));
        var startPageStr = GUILayout.TextField(_startPage.ToString(CultureInfo.InvariantCulture), GUILayout.Width(30), GUILayout.Height(20));
        if (int.TryParse(startPageStr, out var startPageValue))
            _startPage = startPageValue;

        GUILayout.Label("Pages To Skip: ", GUILayout.Width(110), GUILayout.Height(20));
        var pagesToSkipStr = GUILayout.TextField(_pagesToSkip.ToString(CultureInfo.InvariantCulture), GUILayout.Width(30), GUILayout.Height(20));
        if (int.TryParse(pagesToSkipStr, out var pagesToSkipValue))
            _pagesToSkip = pagesToSkipValue;

        GUILayout.EndHorizontal();
    }

    private IEnumerator StartSeach()
    {
        _isSearching = true;
        yield return Search();
        _isSearching = false;
    }

    private IEnumerator Search()
    {
        while (!_wantsToCancelSearch && AUCTION_HOUSE != null)
        {
            if (AUCTION_HOUSE.ListingParent == null)
            {
                MelonLogger.Msg("Failed to get listing parent.");
                yield break;
            }

            if (AUCTION_HOUSE.TrySearch(new AuctionHouse.SearchAH(_implicits, _affixes)))
            {
                MelonLogger.Msg("Items found.");
                yield break;
            }

            if (_continueTillFound)
            {
                MelonLogger.Msg("No items found.");

                var curPage = AUCTION_HOUSE.ListingParent.CurrentQueryItemsResponseDrawn.currentPage;
                var totalPages = AUCTION_HOUSE.ListingParent.CurrentQueryItemsResponseDrawn.totalPages;

                if (curPage == totalPages)
                    yield break;

                MelonLogger.Msg("Continuing search on next page.");
                AUCTION_HOUSE.ListingParent.parentUI.RequestPage(curPage < _startPage ? _startPage : Math.Min(curPage + _pagesToSkip, totalPages));

                var tempah = AUCTION_HOUSE;
                while (tempah == AUCTION_HOUSE)
                    yield return new WaitForSeconds(0.1f);

                yield return new WaitForSeconds(0.5f);
                continue;
            }

            yield break;
        }
    }

    // if (search.continueTillFound && !WantsToCancelSearch)
    // {
    //     MelonLogger.Msg("No items found, continuing search on next page.");
    //     // pagesParent.GetComponent<PaginationControls>().nextButton.button.onClick.Invoke();
    //     // ListingParent.paginationControls.NextPagePress();
    //     // ListingParent.CurrentQueryItemsResponseDrawn.currentPage
    //     var curPage = ListingParent.CurrentQueryItemsResponseDrawn.currentPage;
    //     if (curPage != ListingParent.CurrentQueryItemsResponseDrawn.totalPages)
    //     {
    //         ListingParent.parentUI.RequestPage(curPage + 1);
    //         yield return new WaitForSeconds(1f);
    //         yield return Search(search);
    //         yield break;
    //     }
    //
    //     MelonLogger.Msg("Reached last page.");
    // }

    private void HandleAffixes(bool isImplicits)
    {
        var affixes = isImplicits ? _implicits : _affixes;

        if (GUILayout.Button(isImplicits ? "Implicits" : "Affixes", GUILayout.Width(100), GUILayout.Height(20)))
            affixes.Add(new AuctionHouse.AffixFilterAH("", false, 0f, false, 0f));

        _affixesScrollPosition = GUILayout.BeginScrollView(_affixesScrollPosition);

        for (var i = 0; i < affixes.Count; i++)
        {
            GUILayout.BeginHorizontal();

            var af = affixes[i];

            GUILayout.Label("Text: ", GUILayout.Width(50), GUILayout.Height(20));
            af = af with { Text = GUILayout.TextField(af.Text, GUILayout.Width(100), GUILayout.Height(20)) };

            GUILayout.Label("Exact Text: ", GUILayout.Width(80), GUILayout.Height(20));
            af = af with { ExactText = GUILayout.Toggle(af.ExactText, "", GUILayout.Width(20), GUILayout.Height(20)) };

            GUILayout.Label("Value: ", GUILayout.Width(50), GUILayout.Height(20));
            var valueStr = GUILayout.TextField(af.ValueMin.ToString(CultureInfo.InvariantCulture), GUILayout.Width(50), GUILayout.Height(20));
            if (float.TryParse(valueStr, out var value))
                af = af with { ValueMin = value };

            GUILayout.Label("Search In Range: ", GUILayout.Width(110), GUILayout.Height(20));
            af = af with { SearchInRange = GUILayout.Toggle(af.SearchInRange, "", GUILayout.Width(20), GUILayout.Height(20)) };

            if (af.SearchInRange)
            {
                GUILayout.Label("Value Max: ", GUILayout.Width(70), GUILayout.Height(20));
                var valueMaxStr = GUILayout.TextField(af.ValueMax.ToString(CultureInfo.InvariantCulture), GUILayout.Width(50), GUILayout.Height(20));
                if (float.TryParse(valueMaxStr, out var valueMax))
                    af = af with { ValueMax = valueMax };
            }

            affixes[i] = af;

            if (GUILayout.Button("X", GUILayout.Width(25), GUILayout.Height(20)))
                affixes.RemoveAt(i);

            GUILayout.EndHorizontal();
        }

        GUILayout.EndScrollView();

        if (isImplicits)
            _implicits = affixes;
        else
            _affixes = affixes;
    }
}
