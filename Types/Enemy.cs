using Il2Cpp;
using Il2CppDMM;
using Object = UnityEngine.Object;

namespace TestLE;

public class Enemy
{
    public ActorVisuals Data { get; private set; }
    public DMMapIcon MinimapObject { get; private set; }


    public Enemy(ActorVisuals data, DMMapIcon minimapObject)
    {
        Data = data;
        MinimapObject = minimapObject;

        ENEMIES.Add(this);
        data.add_deathEvent(new Action<ActorVisuals>(OnDeath));
    }

    private void OnDeath(ActorVisuals _)
    {
        ENEMIES.Remove(this);

        if (MinimapObject != null)
        {
            Object.Destroy(MinimapObject.iconGO);
            Object.Destroy(MinimapObject.gameObject);
        }

        if (Data != null)
            Data.remove_deathEvent(new Action<ActorVisuals>(OnDeath));
    }

    public void RemoveEnemy()
    {
        OnDeath(Data);
        ENEMIES.Remove(this);
    }
}
