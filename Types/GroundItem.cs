using System.Collections;
using Il2Cpp;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE;

public class GroundItem
{
    public GroundItemLabel? Item { get; }
    public GroundGoldLabel? Gold { get; }
    public GroundPotionLabel? Potion { get; }


    public GroundItem(GroundItemLabel item)
    {
        Item = item;

        if (GROUND_ITEMS.Any(i => i != null && i.Item == item))
            return;

        GROUND_ITEMS.Add(this);
        LAST_GROUND_ITEM_DROP = DateTime.Now;
    }

    public GroundItem(GroundGoldLabel gold)
    {
        Gold = gold;

        if (GROUND_ITEMS.Any(i => i != null && i.Gold == gold))
            return;

        GROUND_ITEMS.Add(this);
        LAST_GROUND_ITEM_DROP = DateTime.Now;
    }

    public GroundItem(GroundPotionLabel potion)
    {
        Potion = potion;

        if (GROUND_ITEMS.Any(i => i != null && i.Potion == potion))
            return;

        GROUND_ITEMS.Add(this);
        LAST_GROUND_ITEM_DROP = DateTime.Now;
    }

    public void Pickup()
    {
        if (IsItemValid())
            Item!.ClickedItem();
        else if (IsGoldValid())
            Gold!.Clicked();
        else if (IsPotionValid())
            Potion!.Clicked();

        RemoveItem();
    }

    // public Vector3? GetPos()
    // {
    //     Vector3? position = null;
    //     if (IsItemValid())
    //         position = Item!.visuals.transform.position;
    //
    //     if (IsGoldValid())
    //         position = Gold!.visuals.transform.position;
    //
    //     if (position != null)
    //         return position;
    //
    //     RemoveItem();
    //     return null;
    // }

    public IEnumerator MoveToItem()
    {
        Vector3? position = null;
        if (IsItemValid())
            position = Item!.visuals.transform.position;
        else if (IsGoldValid())
            position = Gold!.visuals.transform.position;
        else if (IsPotionValid())
            position = Potion!.visuals.transform.position;

        if (position != null)
            yield return PlayerHelpers.MoveToForce(position.Value);
        else
            RemoveItem();
    }

    public void RemoveItem()
    {
        GROUND_ITEMS.Remove(this);
    }

    private bool IsItemValid()
    {
        return Item != null && Item.gameObject.active;
    }

    private bool IsGoldValid()
    {
        return Gold != null && Gold.gameObject.active;
    }

    private bool IsPotionValid()
    {
        return Potion != null && Potion.gameObject.active;
    }
}
