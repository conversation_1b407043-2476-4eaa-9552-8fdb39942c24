using MelonLoader;
using UnityEngine;

namespace TestLE;

public class Assets
{
    private static readonly Lazy<Assets> lazyInstance = new(() => new Assets());
    public static Assets Instance => lazyInstance.Value;


    public static Sprite? DotSprite => Instance._dotSprite;

    private Sprite? __dotSprite;

    private Sprite? _dotSprite
    {
        get
        {
            if (__dotSprite == null)
                MakeDotSprite();

            return __dotSprite;
        }
    }


    private void MakeDotSprite()
    {
        MelonLogger.Msg("Making dot sprite...");

        const int size = 16;
        const float radius = size / 2f;
        const float fadeThreshold = radius * 0.9f;

        var texture = new Texture2D(size, size, TextureFormat.ARGB32, false);
        var center = new Vector2(size / 2f, size / 2f);

        for (var y = 0; y < size; y++)
        {
            for (var x = 0; x < size; x++)
            {
                var dist = Vector2.Distance(new Vector2(x, y), center);
                // var alpha = dist > radius ? 0 : 1 - dist / (radius * 3);
                var alpha = dist > fadeThreshold ? 1 - (dist - fadeThreshold) / (radius - fadeThreshold) : 1;

                texture.SetPixel(x, y, new Color(1, 1, 1, alpha));
            }
        }

        texture.Apply();

        __dotSprite = Sprite.Create(texture, new Rect(0.0f, 0.0f, texture.width, texture.height), new Vector2(0.5f, 0.5f), 100.0f);
    }
}
