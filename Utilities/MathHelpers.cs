using UnityEngine;

namespace TestLE.Utilities;

public static class MathHelpers
{
    // public static Vector3 GetPositionBetweenTargetAndPlayer(Vector3 targetPosition, float distanceFromCenter)
    // {
    //     var directionToPlayer = (PLAYER!.transform.position - targetPosition).normalized;
    //     var pos = targetPosition - directionToPlayer * (distanceFromCenter * 0.8f);
    //     
    //     return pos;
    // }

    // public static Transform? GetClosest(this List<Transform> transforms, Transform target)
    // {
    //     Transform? closestTransform = null;
    //     var minDistance = float.MaxValue;
    //
    //     foreach (var transform in transforms)
    //     {
    //         var distance = Vector3.Distance(transform.position, target.position);
    //         if (distance < minDistance)
    //         {
    //             minDistance = distance;
    //             closestTransform = transform;
    //         }
    //     }
    //
    //     return closestTransform;
    // }

    public static T? GetClosestToPlayer<T>(this List<T> objects) where T : Component?
    {
        T? closest = null;
        var minDistance = float.MaxValue;

        foreach (var o in objects)
        {
            if (o == null)
                continue;

            var distance = Vector3.Distance(PLAYER.transform.position, o.transform.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                closest = o;
            }
        }

        return closest;
    }

    // public static Transform? GetClosestToPlayer(this List<Transform> transforms)
    // {
    //     Transform? closestTransform = null;
    //     var minDistance = float.MaxValue;
    //
    //     foreach (var transform in transforms)
    //     {
    //         var distance = Vector3.Distance(PLAYER.transform.position, transform.position);
    //         if (distance < minDistance)
    //         {
    //             minDistance = distance;
    //             closestTransform = transform;
    //         }
    //     }
    //
    //     return closestTransform;
    // }
}
