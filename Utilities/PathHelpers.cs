using UnityEngine;
using UnityEngine.AI;

namespace TestLE.Utilities;

public static class PathHelpers
{
    // public static float CalculatePathLength(NavMeshAgent agent, Vector3 target)
    // {
    //     var path = new NavMeshPath();
    //     return agent.CalculatePath(target, path) ? CalculatePathLength(path) : 0.0f;
    // }
    //
    // public static float CalculatePathLength(NavMeshPath path)
    // {
    //     var length = 0.0f;
    //     if (path.corners.Length <= 1)
    //         return length;
    //
    //     for (var i = 1; i < path.corners.Length; i++)
    //     {
    //         length += Vector3.Distance(path.corners[i - 1], path.corners[i]);
    //     }
    //
    //     return length;
    // }
}
